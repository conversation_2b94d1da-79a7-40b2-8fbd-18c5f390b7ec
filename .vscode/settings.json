{"npm.packageManager": "pnpm", "[typescript]": {"editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[javascript]": {"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true}, "editor.formatOnSaveMode": "file", "typescript.tsdk": "node_modules/typescript/lib", "[javascript][typescript][typescriptreact]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[css]": {"editor.defaultFormatter": "biomejs.biome"}, "[graphql]": {"editor.defaultFormatter": "biomejs.biome"}, "editor.formatOnSave": true, "editor.formatOnPaste": true, "emmet.showExpandedAbbreviation": "never", "editor.codeActionsOnSave": {"source.fixAll.biome": "explicit", "source.organizeImports.biome": "explicit"}}