# ======================
# Core Application
# ======================
APP_NAME="Spherical CMS"
APP_EMAIL="<EMAIL>"
NODE_ENV="development"
PORT=3000
NEXT_TELEMETRY_DISABLED=1
DOCKER_ENVIRONMENT=true

# ======================
# URLs Configuration
# ======================
SITE_URL="http://localhost:3000"
NEXT_PUBLIC_SERVER_URL="http://localhost:3000"
BETTER_AUTH_URL="http://localhost:3000"
PAYLOAD_PUBLIC_SERVER_URL="http://localhost:3000"
PAYLOAD_PUBLIC_SITE_URL="http://localhost:3000"

# ======================
# Database Configuration
# ======================
# Use service as hostname when running in Docker
DATABASE_URI="********************************************/db"
# For local development without Docker
# DATABASE_URI="mongodb://localhost:27017/senergy"
# Auth service database (if separate)
# AUTH_DATABASE_URI="mongodb://localhost:27017/senergy-auth"

# ======================
# Security Secrets
# ======================
PAYLOAD_SECRET="Lia3FnGvJs0nkj6AUTJWM8hTFxsPlEqe"
BETTER_AUTH_SECRET="Lia3FnGvJs0nkj6AUTJWM8hTFxsPlEqe"
NEXT_PRIVATE_DRAFT_SECRET="Lia3FnGvJs0nkj6AUTJWM8hTFxsPlEqe"
NEXT_PRIVATE_REVALIDATION_KEY="Lia3FnGvJs0nkj6AUTJWM8hTFxsPlEqe"
PAYLOAD_PUBLIC_DRAFT_SECRET="Lia3FnGvJs0nkj6AUTJWM8hTFxsPlEqe"
REVALIDATION_KEY="Lia3FnGvJs0nkj6AUTJWM8hTFxsPlEqe"

# ======================
# Email Configuration
# ======================
RESEND_API_KEY=""

# ======================
# Analytics & Monitoring
# ======================
# Sentry
SENTRY_ORG=""
SENTRY_PROJECT=""
NEXT_PUBLIC_SENTRY_DSN=""
SENTRY_AUTH_TOKEN=""

# PostHog
NEXT_PUBLIC_POSTHOG_KEY=""
NEXT_PUBLIC_POSTHOG_HOST=""

# ======================
# OAuth Providers
# ======================
# Discord
DISCORD_CLIENT_ID=""
DISCORD_CLIENT_SECRET=""

# GitHub
GITHUB_CLIENT_ID=""
GITHUB_CLIENT_SECRET=""

# LinkedIn
LINKEDIN_CLIENT_ID=""
LINKEDIN_CLIENT_SECRET=""

# ======================
# Feature Flags
# ======================
NEXT_PUBLIC_IS_LIVE=""
# Secret used to authenticate cron jobs
CRON_SECRET=YOUR_CRON_SECRET_HERE
# Used to validate preview requests
PREVIEW_SECRET=YOUR_SECRET_HERE
# Added by Payload
