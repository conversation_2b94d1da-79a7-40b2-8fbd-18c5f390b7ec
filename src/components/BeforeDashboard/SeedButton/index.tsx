'use client';

import { toast } from '@payloadcms/ui';
import type React from 'react';
import { Fragment, useCallback, useState } from 'react';

import './index.scss';

const SuccessMessage: React.FC = () => (
  <div>
    Database seeded! You can now{' '}
    <a href="/" rel="noopener" target="_blank">
      visit your website
    </a>
  </div>
);

export const SeedButton: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [seeded, setSeeded] = useState(false);
  const [error, setError] = useState<null | string>(null);

  const handleClick = useCallback(
    async (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();

      if (seeded) {
        toast.info('Database already seeded.');
        return;
      }
      if (loading) {
        toast.info('Seeding already in progress.');
        return;
      }
      if (error) {
        toast.error('An error occurred, please refresh and try again.');
        return;
      }

      setLoading(true);

      try {
        toast.promise(
          new Promise((resolve, reject) => {
            try {
              fetch('/next/seed', { method: 'POST', credentials: 'include' })
                .then((res) => {
                  if (res.ok) {
                    resolve(true);
                    setSeeded(true);
                  } else {
                    reject('An error occurred while seeding.');
                  }
                })
                .catch((error) => {
                  reject(error);
                });
            } catch (error) {
              reject(error);
            }
          }),
          {
            loading: 'Seeding with data....',
            success: <SuccessMessage />,
            error: 'An error occurred while seeding.',
          }
        );
      } catch (err) {
        const error = err instanceof Error ? err.message : String(err);
        setError(error);
      }
    },
    [loading, seeded, error]
  );

  let message = '';
  if (loading) message = ' (seeding...)';
  if (seeded) message = ' (done!)';
  if (error) message = ` (error: ${error})`;

  return (
    <Fragment>
      <button className="seedButton" onClick={handleClick}>
        Seed your database
      </button>
      {message}
    </Fragment>
  );
};
