{"compilerOptions": {"strict": true, "baseUrl": ".", "esModuleInterop": true, "target": "ES2022", "lib": ["DOM", "DOM.Iterable", "ES2022"], "allowJs": true, "skipLibCheck": true, "noEmit": true, "incremental": true, "jsx": "preserve", "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "sourceMap": true, "isolatedModules": true, "plugins": [{"name": "next"}], "paths": {"@payload-config": ["./src/payload.config.ts"], "react": ["./node_modules/@types/react"], "@/*": ["./src/*"]}, "strictNullChecks": true}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "redirects.js", "next-env.d.ts", "next.config.js", "next-sitemap.config.cjs"], "exclude": ["node_modules"]}