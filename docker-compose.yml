version: '3'

services:
    payload:
        image: node:20-alpine
        ports:
            - '3000:3000'
            - '9229:9229'
        volumes:
            - .:/home/<USER>/app:cached
            - node_modules:/home/<USER>/app/node_modules
        working_dir: /home/<USER>/app/
        command: sh -c "npm install -g pnpm && pnpm install && pnpm run dev:container "
        depends_on:
            postgres:
                condition: service_healthy
        env_file:
            - .env
        develop:
            watch:
                -   path: package.json
                    action: rebuild

    postgres:
        image: postgres:latest
        ports:
            - '5432:5432'
        volumes:
            - data:/data/db
        logging:
            driver: none
        healthcheck:
            test: [ "CMD-SHELL", "pg_isready -U postgres" ]
            interval: 10s
            timeout: 5s
            retries: 5
        environment:
            - POSTGRES_USER=postgres
            - POSTGRES_PASSWORD=postgres
            - POSTGRES_DB=db

volumes:
    data:
    node_modules:
