{"name": "alexander-cato", "version": "1.0.0", "description": "Website template for Payload", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "postbuild": "next-sitemap --config next-sitemap.config.cjs", "dev": "docker compose up", "dev:container": "cross-env NODE_OPTIONS=--no-deprecation --inspect=0.0.0.0:9229 next dev", "dev:prod": "cross-env NODE_OPTIONS=--no-deprecation rm -rf .next && pnpm build && pnpm start", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "ii": "cross-env NODE_OPTIONS=--no-deprecation pnpm --ignore-workspace install", "lint": "biome check .", "lint:fix": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "reinstall": "cross-env NODE_OPTIONS=--no-deprecation rm -rf node_modules && rm pnpm-lock.yaml && pnpm --ignore-workspace install", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "test": "pnpm run test:int && pnpm run test:e2e", "test:e2e": "cross-env NODE_OPTIONS=\"--no-deprecation --no-experimental-strip-types\" pnpm exec playwright test --config=playwright.config.ts", "test:int": "cross-env NODE_OPTIONS=--no-deprecation vitest run --config ./vitest.config.mts"}, "dependencies": {"@payloadcms/admin-bar": "3.50.0", "@payloadcms/db-postgres": "3.50.0", "@payloadcms/live-preview-react": "3.50.0", "@payloadcms/next": "3.50.0", "@payloadcms/payload-cloud": "3.50.0", "@payloadcms/plugin-form-builder": "3.50.0", "@payloadcms/plugin-nested-docs": "3.50.0", "@payloadcms/plugin-redirects": "3.50.0", "@payloadcms/plugin-search": "3.50.0", "@payloadcms/plugin-seo": "3.50.0", "@payloadcms/richtext-lexical": "3.50.0", "@payloadcms/ui": "3.50.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "dotenv": "16.4.7", "geist": "^1.4.2", "graphql": "^16.11.0", "lucide-react": "^0.378.0", "next": "15.4.4", "next-sitemap": "^4.2.3", "payload": "3.50.0", "prism-react-renderer": "^2.4.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.45.4", "sharp": "0.34.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@playwright/test": "1.54.1", "@tailwindcss/typography": "^0.5.16", "@testing-library/react": "16.3.0", "@types/escape-html": "^1.0.4", "@types/node": "22.5.4", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@vitejs/plugin-react": "4.5.2", "autoprefixer": "^10.4.21", "copyfiles": "^2.4.1", "jsdom": "26.1.0", "playwright": "1.54.1", "playwright-core": "1.54.1", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "5.7.3", "ultracite": "5.1.2", "vite-tsconfig-paths": "5.1.4", "vitest": "3.2.3"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp", "esbuild", "unrs-resolver"]}}